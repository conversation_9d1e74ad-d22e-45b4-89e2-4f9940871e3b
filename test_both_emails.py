#!/usr/bin/env python3
"""
Test both admin and user email notifications
"""

import os
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from dotenv import load_dotenv
from datetime import datetime

# Load environment variables
load_dotenv()

def test_both_email_notifications():
    """Test both admin and user email notifications"""
    
    print("🧪 Testing BOTH Admin and User Email Notifications")
    print("=" * 70)
    
    # Get config from environment
    smtp_server = os.getenv('SMTP_SERVER', 'smtp.gmail.com')
    smtp_port = int(os.getenv('SMTP_PORT', '587'))
    admin_email = os.getenv('EMAIL_USER')
    email_pass = os.getenv('EMAIL_PASS')
    
    print(f"📧 Admin Email: {admin_email}")
    print(f"📧 SMTP Server: {smtp_server}:{smtp_port}")
    print()
    
    if not admin_email or not email_pass:
        print("❌ ERROR: EMAIL_USER or EMAIL_PASS not set")
        return False
    
    try:
        print("🔌 Connecting to Gmail SMTP...")
        server = smtplib.SMTP(smtp_server, smtp_port, timeout=30)
        server.starttls()
        server.login(admin_email, email_pass)
        
        # Test 1: Admin Notification Email
        print("\n📨 Test 1: Sending ADMIN notification...")
        
        admin_msg = MIMEMultipart()
        admin_msg['From'] = admin_email
        admin_msg['To'] = admin_email
        admin_msg['Subject'] = '🔔 New User Registration - Test User'
        
        admin_body = """
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <title>New Registration Notification</title>
        </head>
        <body style="font-family: Arial, sans-serif; background-color: #f5f5f5; margin: 0; padding: 20px;">
            <div style="max-width: 600px; margin: 0 auto; background-color: white; border-radius: 10px; overflow: hidden; box-shadow: 0 4px 6px rgba(0,0,0,0.1);">
                
                <!-- Header -->
                <div style="padding: 15px; text-align: center; background-color: #f8f9fa; border-bottom: 1px solid #eee;">
                    <h2 style="color: #e91e63; font-size: 24px; font-weight: bold; margin: 0;">
                        Gig<span style="color: #2196f3;">Genius</span>
                    </h2>
                    <p style="color: #666; font-size: 12px; margin: 5px 0 0 0;">Your Talent Marketplace</p>
                </div>
                
                <!-- Content -->
                <div style="padding: 30px;">
                    <h1 style="color: #333; text-align: center; margin-bottom: 20px;">🔔 New User Registration</h1>
                    
                    <div style="background-color: #f8f9fa; padding: 20px; border-radius: 8px;">
                        <h3 style="color: #333; margin-top: 0;">User Details:</h3>
                        <p><strong>Name:</strong> Test User</p>
                        <p><strong>Email:</strong> <EMAIL></p>
                        <p><strong>Type:</strong> Genius</p>
                        <p><strong>Registration Time:</strong> {timestamp}</p>
                    </div>
                    
                    <p style="text-align: center; margin-top: 20px; color: #666;">
                        A new user has registered on GigGenius!
                    </p>
                </div>
                
                <!-- Footer -->
                <div style="padding: 20px; background-color: #f8f9fa; text-align: center; border-top: 1px solid #eee;">
                    <p style="color: #666; font-size: 12px; margin: 0;">© 2025 GigGenius. All rights reserved.</p>
                </div>
            </div>
        </body>
        </html>
        """.format(timestamp=datetime.now())
        
        admin_msg.attach(MIMEText(admin_body, 'html'))
        server.sendmail(admin_email, [admin_email], admin_msg.as_string())
        print("✅ Admin notification sent successfully!")
        
        # Test 2: User Welcome Email
        print("\n📨 Test 2: Sending USER welcome email...")
        
        user_msg = MIMEMultipart()
        user_msg['From'] = admin_email
        user_msg['To'] = admin_email  # Sending to yourself for testing
        user_msg['Subject'] = 'Welcome to GigGenius - Registration Successful!'
        
        user_body = """
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <title>Welcome to GigGenius</title>
        </head>
        <body style="font-family: Arial, sans-serif; background-color: #f5f5f5; margin: 0; padding: 20px;">
            <div style="max-width: 600px; margin: 0 auto; background-color: white; border-radius: 10px; overflow: hidden; box-shadow: 0 4px 6px rgba(0,0,0,0.1);">
                
                <!-- Header -->
                <div style="padding: 15px; text-align: center; background-color: #f8f9fa; border-bottom: 1px solid #eee;">
                    <h2 style="color: #e91e63; font-size: 24px; font-weight: bold; margin: 0;">
                        Gig<span style="color: #2196f3;">Genius</span>
                    </h2>
                    <p style="color: #666; font-size: 12px; margin: 5px 0 0 0;">Your Talent Marketplace</p>
                </div>
                
                <!-- Content -->
                <div style="padding: 30px;">
                    <h1 style="color: #333; text-align: center; margin-bottom: 20px;">🎉 Welcome to GigGenius!</h1>
                    
                    <p style="font-size: 16px; color: #333; line-height: 1.6;">Dear User,</p>
                    
                    <p style="font-size: 16px; color: #333; line-height: 1.6;">
                        Thank you for registering with GigGenius! Your registration has been received successfully.
                    </p>
                    
                    <div style="background-color: #e3f2fd; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #2196f3;">
                        <h3 style="color: #1976d2; margin-top: 0;">What's Next?</h3>
                        <ul style="color: #333; line-height: 1.6;">
                            <li>Your registration is being reviewed by our team</li>
                            <li>You'll receive an approval notification soon</li>
                            <li>Once approved, you can start using GigGenius</li>
                        </ul>
                    </div>
                    
                    <p style="font-size: 16px; color: #333; line-height: 1.6;">
                        <strong>Registration Time:</strong> {timestamp}
                    </p>
                    
                    <p style="font-size: 16px; color: #333; line-height: 1.6;">
                        Thank you for joining GigGenius!
                    </p>
                </div>
                
                <!-- Footer -->
                <div style="padding: 20px; background-color: #f8f9fa; text-align: center; border-top: 1px solid #eee;">
                    <p style="color: #666; font-size: 12px; margin: 0;">© 2025 GigGenius. All rights reserved.</p>
                </div>
            </div>
        </body>
        </html>
        """.format(timestamp=datetime.now())
        
        user_msg.attach(MIMEText(user_body, 'html'))
        server.sendmail(admin_email, [admin_email], user_msg.as_string())
        print("✅ User welcome email sent successfully!")
        
        server.quit()
        
        print(f"\n📬 Check your inbox at: {admin_email}")
        print("You should receive 2 emails:")
        print("1. 🔔 Admin notification (for you)")
        print("2. 🎉 User welcome email (what users will receive)")
        
        return True
        
    except Exception as e:
        print(f"❌ ERROR: {e}")
        return False

def show_email_flow():
    """Show the complete email flow"""
    
    print("\n📧 COMPLETE EMAIL FLOW:")
    print("=" * 50)
    
    print("When a user registers:")
    print("1. 📝 User fills registration form")
    print("2. 💾 Data saved to database")
    print("3. 📧 TWO emails sent simultaneously:")
    print("   ├── 🔔 Admin notification → <EMAIL>")
    print("   └── 🎉 User welcome email → user's email address")
    print()
    
    print("📨 Email Details:")
    print("├── FROM: <EMAIL> (your admin email)")
    print("├── TO (Admin): <EMAIL>")
    print("├── TO (User): whatever email they entered")
    print("└── LOGO: Fixed styled text logo")

if __name__ == "__main__":
    print("🚀 GigGenius BOTH Admin & User Email Test")
    print("=" * 80)
    print(f"⏰ Test started at: {datetime.now()}")
    
    # Show email flow
    show_email_flow()
    
    # Test both emails
    success = test_both_email_notifications()
    
    print("\n" + "=" * 80)
    print("🎯 SUMMARY:")
    if success:
        print("✅ BOTH admin and user emails working!")
        print("✅ Admin gets notified of new registrations")
        print("✅ Users receive welcome confirmation")
        print("✅ Logo fixed in both email templates")
        print("✅ Professional HTML email templates")
    else:
        print("❌ Email test failed")
    
    print(f"⏰ Test completed at: {datetime.now()}")
    print("=" * 80)
