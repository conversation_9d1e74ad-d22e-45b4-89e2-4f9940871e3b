#!/usr/bin/env python3
"""
Test email configuration with environment variables
"""

import os
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def test_email_connection():
    """Test the email configuration"""
    
    print("🧪 Testing Email Configuration")
    print("=" * 50)
    
    # Get email config from environment variables
    smtp_server = 'smtp.hostinger.com'
    smtp_port = 587
    email_user = os.getenv('EMAIL_USER')
    email_pass = os.getenv('EMAIL_PASS')
    
    print(f"📧 SMTP Server: {smtp_server}")
    print(f"📧 SMTP Port: {smtp_port}")
    print(f"📧 Email User: {email_user}")
    print(f"📧 Password: {'*' * len(email_pass) if email_pass else 'NOT SET'}")
    print()
    
    if not email_user or not email_pass:
        print("❌ ERROR: EMAIL_USER or EMAIL_PASS not set in .env file")
        return False
    
    try:
        print("🔌 Connecting to SMTP server...")
        server = smtplib.SMTP(smtp_server, smtp_port, timeout=30)
        
        print("🔐 Starting TLS...")
        server.starttls()
        
        print("🔑 Logging in...")
        server.login(email_user, email_pass)
        
        print("📨 Sending test email...")
        
        # Create test email
        msg = MIMEMultipart()
        msg['From'] = email_user
        msg['To'] = email_user  # Send to self
        msg['Subject'] = 'GigGenius Email Test - ' + str(datetime.now())
        
        body = """
        <html>
        <body>
        <h2>🎉 Email Configuration Test Successful!</h2>
        <p>This is a test email from your GigGenius application.</p>
        <p><strong>Timestamp:</strong> {timestamp}</p>
        <p><strong>From:</strong> {email}</p>
        <p>If you received this email, your email configuration is working correctly!</p>
        </body>
        </html>
        """.format(timestamp=datetime.now(), email=email_user)
        
        msg.attach(MIMEText(body, 'html'))
        
        # Send email
        server.sendmail(email_user, [email_user], msg.as_string())
        server.quit()
        
        print("✅ SUCCESS: Email sent successfully!")
        print(f"📬 Check your inbox at: {email_user}")
        return True
        
    except smtplib.SMTPAuthenticationError as e:
        print(f"❌ AUTHENTICATION ERROR: {e}")
        print("🔍 Possible causes:")
        print("   - Wrong email or password")
        print("   - Account still blocked")
        print("   - 2FA enabled (use app password)")
        return False
        
    except smtplib.SMTPRecipientsRefused as e:
        if "550 5.7.1" in str(e) or "blocked" in str(e).lower():
            print(f"❌ ACCOUNT STILL BLOCKED: {e}")
            print("🚨 <NAME_EMAIL> is still blocked by MailChannels")
            print("📞 Contact Hostinger support immediately")
            return False
        else:
            print(f"❌ RECIPIENT ERROR: {e}")
            return False
            
    except smtplib.SMTPException as e:
        print(f"❌ SMTP ERROR: {e}")
        return False
        
    except Exception as e:
        print(f"❌ UNEXPECTED ERROR: {e}")
        return False

def check_env_file():
    """Check if .env file exists and has required variables"""
    
    print("📁 Checking .env file...")
    
    if not os.path.exists('.env'):
        print("❌ .env file not found!")
        print("📝 Create .env file with:")
        print("EMAIL_USER=<EMAIL>")
        print("EMAIL_PASS=Happiness1524$")
        return False
    
    email_user = os.getenv('EMAIL_USER')
    email_pass = os.getenv('EMAIL_PASS')
    
    if not email_user:
        print("❌ EMAIL_USER not set in .env")
        return False
        
    if not email_pass:
        print("❌ EMAIL_PASS not set in .env")
        return False
    
    print("✅ .env file configured correctly")
    return True

if __name__ == "__main__":
    from datetime import datetime
    
    print("🚀 GigGenius Email Configuration Test")
    print("=" * 60)
    print(f"⏰ Test started at: {datetime.now()}")
    print()
    
    # Check environment file
    if not check_env_file():
        print("\n❌ Environment configuration failed")
        exit(1)
    
    # Test email connection
    success = test_email_connection()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 EMAIL CONFIGURATION WORKING!")
        print("✅ Your app.py should now send emails successfully")
    else:
        print("❌ EMAIL CONFIGURATION FAILED!")
        print("🔧 Follow the troubleshooting steps above")
    
    print(f"⏰ Test completed at: {datetime.now()}")
