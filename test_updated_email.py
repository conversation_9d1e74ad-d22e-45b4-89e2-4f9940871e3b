#!/usr/bin/env python3
"""
Test the updated email system - only admin notifications, no user emails
"""

import os
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from dotenv import load_dotenv
from datetime import datetime

# Load environment variables
load_dotenv()

def test_admin_notification():
    """Test admin notification email with fixed logo"""
    
    print("🧪 Testing Updated Admin Email System")
    print("=" * 60)
    
    # Get config from environment
    smtp_server = os.getenv('SMTP_SERVER', 'smtp.gmail.com')
    smtp_port = int(os.getenv('SMTP_PORT', '587'))
    admin_email = os.getenv('EMAIL_USER')
    email_pass = os.getenv('EMAIL_PASS')
    
    print(f"📧 Admin Email: {admin_email}")
    print(f"📧 SMTP Server: {smtp_server}:{smtp_port}")
    print()
    
    if not admin_email or not email_pass:
        print("❌ ERROR: EMAIL_USER or EMAIL_PASS not set")
        return False
    
    try:
        print("🔌 Connecting to Gmail SMTP...")
        server = smtplib.SMTP(smtp_server, smtp_port, timeout=30)
        server.starttls()
        server.login(admin_email, email_pass)
        
        print("📨 Sending admin notification test...")
        
        # Create admin notification email with fixed logo
        msg = MIMEMultipart()
        msg['From'] = admin_email
        msg['To'] = admin_email
        msg['Subject'] = '🔔 Test: New User Registration - John Doe'
        
        # HTML email with fixed logo
        body = """
        <!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>New Registration Notification</title>
        </head>
        <body style="margin: 0; padding: 0; font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; background-color: #f5f5f5;">
            <div style="max-width: 600px; margin: 20px auto; background-color: white; border-radius: 10px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); overflow: hidden;">
                <table width="100%" cellpadding="0" cellspacing="0" style="border-collapse: collapse;">
                    
                    <!-- Header with Logo -->
                    <tr>
                        <td style="padding: 15px; text-align: center; background-color: #f8f9fa; border-bottom: 1px solid #eee;">
                            <div style="text-align: center;">
                                <h2 style="color: #e91e63; font-size: 24px; font-weight: bold; margin: 0; font-family: 'Arial', sans-serif;">
                                    Gig<span style="color: #2196f3;">Genius</span>
                                </h2>
                                <p style="color: #666; font-size: 12px; margin: 5px 0 0 0;">Your Talent Marketplace</p>
                            </div>
                        </td>
                    </tr>
                    
                    <!-- Content -->
                    <tr>
                        <td style="padding: 30px;">
                            <h1 style="color: #333; font-size: 24px; margin-bottom: 20px; text-align: center;">
                                🔔 New User Registration
                            </h1>
                            
                            <div style="background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
                                <h3 style="color: #333; margin-top: 0;">User Details:</h3>
                                <p><strong>Name:</strong> John Doe</p>
                                <p><strong>Email:</strong> <EMAIL></p>
                                <p><strong>Type:</strong> Genius</p>
                                <p><strong>Country:</strong> Philippines</p>
                                <p><strong>Registration Time:</strong> {timestamp}</p>
                            </div>
                            
                            <div style="text-align: center; margin-top: 30px;">
                                <p style="color: #666; font-size: 14px;">
                                    This is a test of the updated email system.<br>
                                    ✅ User welcome emails removed<br>
                                    ✅ Logo fixed<br>
                                    ✅ Only admin notifications sent
                                </p>
                            </div>
                        </td>
                    </tr>
                    
                    <!-- Footer -->
                    <tr>
                        <td style="padding: 20px; background-color: #f8f9fa; text-align: center; border-top: 1px solid #eee;">
                            <p style="color: #666; font-size: 12px; margin: 0;">
                                © 2025 GigGenius. All rights reserved.
                            </p>
                        </td>
                    </tr>
                </table>
            </div>
        </body>
        </html>
        """.format(timestamp=datetime.now())
        
        msg.attach(MIMEText(body, 'html'))
        
        # Send email
        server.sendmail(admin_email, [admin_email], msg.as_string())
        server.quit()
        
        print("✅ SUCCESS: Admin notification sent!")
        print(f"📬 Check your inbox at: {admin_email}")
        return True
        
    except Exception as e:
        print(f"❌ ERROR: {e}")
        return False

def show_changes():
    """Show what was changed"""
    
    print("\n🔧 CHANGES MADE:")
    print("=" * 50)
    
    print("1. ❌ REMOVED: User welcome emails")
    print("   - Users no longer receive registration confirmation emails")
    print("   - Only admin gets notified of new registrations")
    print()
    
    print("2. ✅ FIXED: Email logo")
    print("   - Replaced broken image link with styled text logo")
    print("   - 'GigGenius' with pink 'Gig' and blue 'Genius'")
    print("   - Added tagline 'Your Talent Marketplace'")
    print()
    
    print("3. 📧 EMAIL FLOW NOW:")
    print("   - User registers → Only admin gets notification")
    print("   - No emails sent to users")
    print("   - Clean, professional admin notifications")

if __name__ == "__main__":
    print("🚀 GigGenius Updated Email System Test")
    print("=" * 70)
    print(f"⏰ Test started at: {datetime.now()}")
    print()
    
    # Show changes made
    show_changes()
    print()
    
    # Test admin notification
    success = test_admin_notification()
    
    print("\n" + "=" * 70)
    print("🎯 SUMMARY:")
    if success:
        print("✅ Updated email system working perfectly!")
        print("✅ Logo fixed in email templates")
        print("✅ User welcome emails removed")
        print("✅ Only admin notifications sent")
    else:
        print("❌ Email test failed")
    
    print(f"⏰ Test completed at: {datetime.now()}")
    print("=" * 70)
