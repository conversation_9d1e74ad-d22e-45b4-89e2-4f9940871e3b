#!/usr/bin/env python3
"""
Test Gmail admin email configuration
"""

import os
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from dotenv import load_dotenv
from datetime import datetime

# Load environment variables
load_dotenv()

def test_gmail_admin():
    """Test Gmail admin email configuration"""
    
    print("🧪 Testing Gmail Admin Email Configuration")
    print("=" * 60)
    
    # Get config from environment
    smtp_server = os.getenv('SMTP_SERVER', 'smtp.gmail.com')
    smtp_port = int(os.getenv('SMTP_PORT', '587'))
    email_user = os.getenv('EMAIL_USER')
    email_pass = os.getenv('EMAIL_PASS')
    
    print(f"📧 Admin Email: {email_user}")
    print(f"📧 SMTP Server: {smtp_server}:{smtp_port}")
    print(f"📧 Password: {'*' * len(email_pass) if email_pass else 'NOT SET'}")
    print()
    
    if not email_user or not email_pass:
        print("❌ ERROR: EMAIL_USER or EMAIL_PASS not set in .env file")
        print("🔧 Update your .env file with Gmail credentials")
        return False
    
    if email_pass == 'your-gmail-app-password-here':
        print("❌ ERROR: You need to set up Gmail App Password")
        print("🔧 Follow these steps:")
        print("1. Go to Gmail Settings > Security")
        print("2. Enable 2-Factor Authentication")
        print("3. Generate App Password for 'Mail'")
        print("4. Update EMAIL_PASS in .env file")
        return False
    
    try:
        print("🔌 Connecting to Gmail SMTP...")
        server = smtplib.SMTP(smtp_server, smtp_port, timeout=30)
        
        print("🔐 Starting TLS...")
        server.starttls()
        
        print("🔑 Logging in...")
        server.login(email_user, email_pass)
        
        print("📨 Sending test email...")
        
        # Create test email
        msg = MIMEMultipart()
        msg['From'] = email_user
        msg['To'] = email_user  # Send to self first
        msg['Subject'] = 'GigGenius Admin Email Test - ' + str(datetime.now())
        
        body = """
        <html>
        <body>
        <h2>🎉 Gmail Admin Email Working!</h2>
        <p>This is a test email from your GigGenius application admin.</p>
        <p><strong>Admin Email:</strong> {email}</p>
        <p><strong>Timestamp:</strong> {timestamp}</p>
        <p><strong>Status:</strong> ✅ Gmail SMTP configuration successful!</p>
        <hr>
        <p><em>Your GigGenius app can now send emails to users!</em></p>
        </body>
        </html>
        """.format(email=email_user, timestamp=datetime.now())
        
        msg.attach(MIMEText(body, 'html'))
        
        # Send email
        server.sendmail(email_user, [email_user], msg.as_string())
        server.quit()
        
        print("✅ SUCCESS: Gmail admin email working!")
        print(f"📬 Check your inbox at: {email_user}")
        return True
        
    except smtplib.SMTPAuthenticationError as e:
        print(f"❌ AUTHENTICATION ERROR: {e}")
        print("🔍 Possible causes:")
        print("   - Wrong Gmail App Password")
        print("   - 2FA not enabled")
        print("   - App Password not generated")
        return False
        
    except Exception as e:
        print(f"❌ ERROR: {e}")
        return False

def test_external_delivery():
    """Test sending to external email (simulate user registration)"""
    
    print("\n🧪 Testing External Email Delivery")
    print("=" * 50)
    
    # Simulate sending to a user's Gmail
    test_user_email = "<EMAIL>"  # Your own Gmail for testing
    
    smtp_server = os.getenv('SMTP_SERVER', 'smtp.gmail.com')
    smtp_port = int(os.getenv('SMTP_PORT', '587'))
    admin_email = os.getenv('EMAIL_USER')
    email_pass = os.getenv('EMAIL_PASS')
    
    print(f"📧 From (Admin): {admin_email}")
    print(f"📧 To (User): {test_user_email}")
    
    if email_pass == 'your-gmail-app-password-here':
        print("⚠️  Skipping external test - set up App Password first")
        return False
    
    try:
        server = smtplib.SMTP(smtp_server, smtp_port, timeout=30)
        server.starttls()
        server.login(admin_email, email_pass)
        
        # Create user registration email
        msg = MIMEMultipart()
        msg['From'] = admin_email
        msg['To'] = test_user_email
        msg['Subject'] = 'Welcome to GigGenius - Registration Successful!'
        
        body = """
        <html>
        <body>
        <h2>🎉 Welcome to GigGenius!</h2>
        <p>Dear User,</p>
        <p>Your registration has been successful!</p>
        <p><strong>Admin Contact:</strong> {admin_email}</p>
        <p><strong>Registration Time:</strong> {timestamp}</p>
        <p>Thank you for joining GigGenius!</p>
        </body>
        </html>
        """.format(admin_email=admin_email, timestamp=datetime.now())
        
        msg.attach(MIMEText(body, 'html'))
        
        print("📨 Sending registration email to user...")
        server.sendmail(admin_email, [test_user_email], msg.as_string())
        server.quit()
        
        print("✅ SUCCESS: External email delivered!")
        print(f"📬 User should receive email at: {test_user_email}")
        return True
        
    except Exception as e:
        print(f"❌ External email failed: {e}")
        return False

if __name__ == "__main__":
    print("🚀 GigGenius Gmail Admin Email Test")
    print("=" * 70)
    print(f"⏰ Test started at: {datetime.now()}")
    print()
    
    # Test admin email
    admin_success = test_gmail_admin()
    
    if admin_success:
        # Test external delivery
        external_success = test_external_delivery()
    else:
        external_success = False
    
    print("\n" + "=" * 70)
    print("🎯 SUMMARY:")
    print(f"✅ Admin Email: {'Working' if admin_success else 'Failed'}")
    print(f"✅ External Delivery: {'Working' if external_success else 'Failed'}")
    
    if admin_success and external_success:
        print("\n🎉 PERFECT! Your GigGenius app can now send emails!")
        print("✅ Admin email: <EMAIL>")
        print("✅ Users will receive registration emails")
        print("✅ No more Hostinger blocking issues")
    else:
        print("\n🔧 Next steps:")
        if not admin_success:
            print("1. Set up Gmail App Password")
        print("2. Update .env file with correct credentials")
        print("3. Run test again")
    
    print(f"⏰ Test completed at: {datetime.now()}")
    print("=" * 70)
